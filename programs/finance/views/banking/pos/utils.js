import _ from 'lodash';

export function getColumns(vm) {
    const company = vm.$store.getters['session/company'];

    return [
        {
            field: 'dueDate',
            label: 'Due Date',
            format: 'date',
            sort: 'desc',
            width: 120
        },
        {
            field: 'recordDate',
            label: 'Record Date',
            format: 'date',
            visible: false,
            width: 120
        },
        {
            field: 'issueDate',
            label: 'Issue Date',
            format: 'date',
            visible: false,
            width: 120
        },
        {
            field: 'documentNo',
            label: 'Document no',
            width: 150
        },
        {
            field: 'branch.name',
            label: 'Branch Office',
            hidden: !vm.$setting('system.multiBranch'),
            visible: false,
            width: 180
        },
        {
            field: 'code',
            label: 'Voucher No',
            view: 'accounting.adviser.journal-entries-detail',
            relationParams(params) {
                const data = params.data;

                return {id: data.journalEntryId};
            },
            visible: false
        },
        {
            field: 'journal.name',
            label: 'Account',
            visible: false,
            width: 180
        },
        {
            field: 'partner',
            label: 'Partner',
            subSelect: ['code', 'name'],
            visible: false,
            relationParams(params) {
                const data = params.data;
                const relation = {};

                relation.isVisible =
                    _.isObject(data.partner) && _.isString(data.partner.code) && _.isString(data.partner.name);

                if (relation.isVisible) {
                    relation.view = 'partners.partners-detail';
                    relation.id = data.partnerId;
                    relation.template = '{{code}} - {{name}}';
                }

                return relation;
            },
            width: 180
        },
        {
            field: 'paymentAccount.name',
            label: 'Payment Account',
            visible: false,
            width: 240
        },
        {
            field: 'reference',
            label: 'Reference'
        },
        {
            field: 'description',
            label: 'Description'
        },
        {
            field: 'tagIds',
            label: 'Tags',
            tagsCell: true,
            tagLabels: vm.tagOptions,
            visible: false
        },
        {
            field: 'installmentNo',
            label: 'Installment',
            render(params) {
                const data = params.data;

                if (_.isObject(data) && _.isInteger(data.installmentNo)) {
                    return `${data.installmentNo} / ${data.installmentCount}`;
                }

                return '';
            },
            width: 120
        },
        {
            field: 'currency.name',
            label: 'Currency',
            hidden: !vm.$setting('system.multiCurrency'),
            visible: false,
            width: 120
        },
        {
            field: 'installmentAmount',
            label: 'Amount',
            format: 'currency',
            formatOptions(row) {
                if (_.isObject(row) && row.currencyId) {
                    console.log(row);
                    console.log(vm.currencies);
                    const currency = vm.currencies.find(c => c._id === row.currencyId);
                    let options = {currency: {}};

                    options.currency.symbol = currency.symbol;
                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                    return options;
                }

                return {};
            },
            width: 150
        },
        {
            field: 'commissionAmount',
            label: 'Commission amount',
            format: 'currency',
            formatOptions(row) {
                if (_.isObject(row) && row.currencyId) {
                    const currency = vm.currencies.find(c => c._id === row.currencyId);
                    let options = {currency: {}};

                    options.currency.symbol = currency.symbol;
                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                    return options;
                }

                return {};
            },
            width: 150
        },
        {
            field: 'refundAmount',
            label: 'Refund amount',
            format: 'currency',
            formatOptions(row) {
                if (_.isObject(row) && row.currencyId) {
                    
                    const currency = vm.currencies.find(c => c._id === row.currencyId);
                    let options = {currency: {}};

                    options.currency.symbol = currency.symbol;
                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                    return options;
                }

                return {};
            },
            width: 150
        },
        {
            field: 'systemCurrencyAmount',
            label: 'Amount (SC)',
            visible: false,
            hidden: !vm.$setting('system.multiCurrency'),
            format: 'currency',
            formatOptions(row) {
                const currency = company.currency;
                let options = {currency: {}};

                options.currency.symbol = currency.symbol;
                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                return options;
            },
            width: 120
        },
        {
            field: 'status',
            label: 'Status',
            tagsCell: true,
            translateLabels: true,
            tagLabels: [
                {value: 'open', label: 'Open', color: 'success'},
                {value: 'refunded', label: 'Refunded', color: 'teal'},
                {value: 'partially-refunded', label: 'Partially refunded', color: 'amber'},
                {value: 'charged', label: 'Charged', color: 'primary'},
                {value: 'canceled', label: 'Canceled', color: 'danger'}
            ],
            width: 120
        }
    ];
}

export function getApplicableScopeFilters(vm) {
    return [
        {
            field: 'dueDate',
            label: 'Due date',
            type: 'date'
        },
        {
            field: 'recordDate',
            label: 'Record date',
            type: 'date'
        },
        {
            field: 'issueDate',
            label: 'Issue date',
            type: 'date'
        },
        {
            field: 'documentNo',
            label: 'Document no'
        },
        {
            field: 'branchId',
            label: 'Branch office',
            collection: 'kernel.branches',
            filters: {
                _id: {$in: vm.$user.branchIds},
                $sort: {name: 1}
            },
            condition() {
                return vm.$setting('system.multiBranch');
            }
        },
        {field: 'code', label: 'Voucher no'},
        {
            field: 'partnerType',
            label: 'Partner type',
            translateLabels: true,
            items: [
                {value: 'customer', label: 'Customer'},
                {value: 'vendor', label: 'Vendor'},
                {value: 'employee', label: 'Employee'}
            ]
        },
        {
            field: 'partnerId',
            label: 'Partner',
            collection: 'kernel.partners',
            extraFields: ['code'],
            filters: {$sort: {name: 1}},
            template: '{{code}} - {{name}}'
        },
        {field: 'reference', label: 'Reference'},
        {field: 'description', label: 'Description'},
        {
            field: 'tagIds',
            label: 'Tags',
            valueLabels: vm.tagOptions
        },
        {
            field: 'currencyId',
            label: 'Currency',
            collection: 'kernel.currencies',
            filters: {$sort: {name: 1}},
            condition() {
                return vm.$setting('system.multiCurrency');
            }
        },
        {
            type: 'decimal',
            field: 'installmentAmount',
            label: 'Amount',
            format: 'currency'
        },
        {
            field: 'systemCurrencyAmount',
            label: 'Amount (SC)',
            condition() {
                return vm.$setting('system.multiCurrency');
            },
            format: 'currency'
        },
        {
            field: 'status',
            label: 'Status',
            translateLabels: true,
            items: [
                {value: 'open', label: 'Open'},
                {value: 'refunded', label: 'Refunded'},
                {value: 'partially-refunded', label: 'Partially refunded'},
                {value: 'charged', label: 'Charged'},
                {value: 'canceled', label: 'Canceled'}
            ]
        }
    ];
}
