import _ from 'lodash';
import {rrule} from 'framework/helpers';

export default [
    {
        name: 'activities-get-list-activities',
        async action({listId, skip = 0, limit = 30, extraQuery = {}, isCompletedShown = false}, params) {
            const app = this.app;
            const user = params.user;
            const userId = user._id;
            const employeeId = !!user.partnerId ? user.partnerId : null;
            const now = app.datetime.local();

            const activitiesQuery = {$or: []};
            activitiesQuery.$or.push(
                ...[
                    {assignedById: userId},
                    {
                        isPersonal: true,
                        assignedToUserIds: userId
                    }
                ]
            );
            activitiesQuery.$or.push({assignedToUserIds: userId});
            if (employeeId) {
                activitiesQuery.$or.push({assignedToEmployeeIds: employeeId});
            }
            if (employeeId) {
                activitiesQuery.$or.push({
                    'attendees.role': 'employee',
                    'attendees.attendeeId': employeeId
                });
            }
            activitiesQuery.assignedById = userId;

            let query = {};

            if (listId === 'today') {
                query = {
                    ...activitiesQuery,
                    startDate: {
                        $gte: now.startOf('day').toJSDate(),
                        $lte: now.endOf('day').toJSDate()
                    }
                };
            } else if (listId === 'scheduled') {
                query = {
                    ...activitiesQuery,
                    isScheduled: true
                };
            } else if (listId === 'important') {
                query = {
                    ...activitiesQuery,
                    isImportant: true
                };
            } else if (listId === 'completed') {
                query = {
                    ...activitiesQuery,
                    status: 'closed'
                };
            } else if (listId === 'notCompleted') {
                query = {
                    ...activitiesQuery,
                    status: {$nin: ['closed', 'canceled']}
                };
            } else if (listId === 'all') {
                query = {...activitiesQuery};
            } else if (listId !== 'all') {
                query = {listId};
            }

            if (!isCompletedShown && !(listId === 'completed' || listId === 'notCompleted')) {
                query.status = {$nin: ['closed', 'canceled']};
            }
            if (!query.status) {
                query.status = {$ne: 'canceled'};
            }

            if (!_.isEmpty(extraQuery)) {
                if (!Array.isArray(query.$and)) query.$and = [];

                query.$and.push(extraQuery);
            }

            return await app.collection('kernel.activities').find({
                ...query,
                $skip: skip,
                $limit: limit,
                $sort: {order: -1},
                $populate: ['list']
            });
        }
    },
    {
        name: 'activities-save-activity',
        async action({data, id}, params) {
            const app = this.app;
            const isCreate = !id;
            const activitiesCollection = app.collection('kernel.activities');

            // Eğer partnerType 'lead' ise hem partnerId hem leadId dolu olmalı
            if (data.partnerType === 'lead') {
                if (data.leadId && !data.partnerId) {
                    data.partnerId = data.leadId;
                } else if (!data.leadId && data.partnerId) {
                    data.leadId = data.partnerId;
                }
            }
            // Get activity color.
            data.color = getTypeColor(data.type);

            // Personal activity.
            if (!!data.isPersonal) {
                data.assignedToType = 'user';
                data.assignedToUserIds = [params.user._id];
                data.assignedToEmployeeIds = [];
                delete data.assignedToRecipientListId;
            }

            if (!isCreate) {
                // Check permission.
                await app.checkPermission({
                    user: params.user,
                    collection: 'kernel.activities',
                    method: 'patch',
                    id
                });

                // Get existing activity.
                const activity = await activitiesCollection.get(id);

                // Check if we have an activity with this id.
                if (!_.isObject(activity)) {
                    throw new app.errors.Unprocessable(this.translate('Activity not found!'));
                }

                // Check if activity is already closed or canceled.
                if (activity.status === 'closed' || activity.status === 'canceled') {
                    throw new app.errors.Unprocessable(
                        this.translate('Closed or canceled activities cannot be updated!')
                    );
                }

                // Check assignees.
                if (Array.isArray(data.assignedToUserIds) || Array.isArray(data.assignedToEmployeeIds)) {
                    let assignedToIds = [];
                    if (Array.isArray(data.assignedToUserIds))
                        assignedToIds = assignedToIds.concat(data.assignedToUserIds);
                    if (Array.isArray(data.assignedToEmployeeIds))
                        assignedToIds = assignedToIds.concat(data.assignedToEmployeeIds);
                    if (assignedToIds.length < 1) {
                        throw new app.errors.Unprocessable(
                            app.translate('{{label}} is required', {
                                label: app.translate('Assigned to')
                            })
                        );
                    }
                }

                // Fix all day end date.
                if (!data.endDate && _.isDate(data.startDate) && !!data.allDay) {
                    data.endDate = this.app.datetime.fromJSDate(data.startDate).endOf('day').toJSDate();
                }

                // Check dates.
                if (_.isDate(data.startDate) || _.isDate(data.endDate)) {
                    const startDate = _.isDate(data.startDate) ? data.startDate : app.datetime.local().toJSDate();
                    const endDate = _.isDate(data.endDate) ? data.endDate : app.datetime.local().toJSDate();

                    if (startDate.getTime() >= endDate.getTime()) {
                        throw new app.errors.Unprocessable(this.translate('End date cannot be before start date!'));
                    }
                }

                // Check partner.
                if (data.type === 'appointment' || data.type === 'phone-call') {
                    if ((data.partnerType === 'customer' || data.partnerType === 'vendor') && !data.partnerId) {
                        throw new app.errors.Unprocessable(
                            app.translate('{{label}} is required', {
                                label: app.translate('Partner')
                            })
                        );
                    } else if (data.partnerType === 'lead' && !data.leadId) {
                        throw new app.errors.Unprocessable(
                            app.translate('{{label}} is required', {
                                label: app.translate('Partner')
                            })
                        );
                    }
                }

                // Create notification job.
                data.notificationJobId = await scheduleActivityNotificationJob(app, _.assign(activity, data));

                // Assigned to.
                const activityParams = activity.params || {};
                if (data.assignedToType === 'employee') {
                    const employee = await app.collection('kernel.partners').findOne({
                        _id: data.assignedToEmployeeIds[0],
                        $select: ['_id', 'code', 'name']
                    });

                    activityParams.assignedTo = {
                        id: employee._id,
                        code: employee.code,
                        name: employee.name
                    };
                } else if (data.assignedToType === 'user') {
                    const user = await app.collection('kernel.users').findOne({
                        _id: data.assignedToUserIds[0],
                        $select: ['_id', 'code', 'name']
                    });

                    activityParams.assignedTo = {
                        id: user._id,
                        code: user.code,
                        name: user.name
                    };
                }
                data.params = activityParams;

                // Update activity.
                return await activitiesCollection.patch({_id: id}, data, {
                    user: params.user,
                    userLocation: params.userLocation,
                    setLocation: true
                });
            } else {
                // Check permission.
                await app.checkPermission({
                    user: params.user,
                    collection: 'kernel.activities',
                    method: 'create'
                });

                // Check assignees.
                let assignedToIds = [];
                if (Array.isArray(data.assignedToUserIds)) assignedToIds = assignedToIds.concat(data.assignedToUserIds);
                if (Array.isArray(data.assignedToEmployeeIds))
                    assignedToIds = assignedToIds.concat(data.assignedToEmployeeIds);
                if (assignedToIds.length < 1) {
                    throw new app.errors.Unprocessable(
                        app.translate('{{label}} is required', {
                            label: app.translate('Assigned to')
                        })
                    );
                }

                // Fix all day end date.
                if (!data.endDate && _.isDate(data.startDate) && !!data.allDay) {
                    data.endDate = this.app.datetime.fromJSDate(data.startDate).endOf('day').toJSDate();
                }

                // Check dates.
                if (data.startDate.getTime() >= data.endDate.getTime()) {
                    throw new app.errors.Unprocessable(this.translate('End date cannot be before start date!'));
                }

                // Check partner.
                if (data.type === 'appointment' || data.type === 'phone-call') {
                    if ((data.partnerType === 'customer' || data.partnerType === 'vendor') && !data.partnerId) {
                        throw new app.errors.Unprocessable(
                            app.translate('{{label}} is required', {
                                label: app.translate('Partner')
                            })
                        );
                    } else if (data.partnerType === 'lead' && !data.leadId) {
                        throw new app.errors.Unprocessable(
                            app.translate('{{label}} is required', {
                                label: app.translate('Partner')
                            })
                        );
                    }
                }

                // Assigned to.
                const activityParams = {};
                if (data.assignedToType === 'employee') {
                    const employee = await app.collection('kernel.partners').findOne({
                        _id: data.assignedToEmployeeIds[0],
                        $select: ['_id', 'code', 'name']
                    });

                    activityParams.assignedTo = {
                        id: employee._id,
                        code: employee.code,
                        name: employee.name
                    };
                } else if (data.assignedToType === 'user') {
                    const user = await app.collection('kernel.users').findOne({
                        _id: data.assignedToUserIds[0],
                        $select: ['_id', 'code', 'name']
                    });

                    activityParams.assignedTo = {
                        id: user._id,
                        code: user.code,
                        name: user.name
                    };
                }
                data.params = activityParams;

                // Create activity.
                const result = await activitiesCollection.create(data, {
                    user: params.user,
                    userLocation: params.userLocation,
                    setLocation: true
                });

                // Create notification job.
                const notificationJobId = await scheduleActivityNotificationJob(app, result);

                // Update notification.
                activitiesCollection.patch(
                    {_id: result._id},
                    {notificationJobId},
                    {user: params.user, setLocation: false, skipEvents: true}
                );

                if (result.repeat !== 'never' && !!result.repeatStr && typeof result.repeatStr === 'string') {
                    const numbering = await app.collection('kernel.numbering').findOne({
                        code: 'activityNumbering',
                        $select: ['_id'],
                        $disableInUseCheck: true,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const rr = rrule(result.repeatStr);
                    let recurringDates = [];

                    if (result.repeat === 'every-day') {
                        recurringDates = rr.between(result.startDate, result.repeatingEndDate).slice(1);
                    } else if (result.repeat === 'every-week') {
                        recurringDates = rr.between(result.startDate, result.repeatingEndDate).slice(1);
                    } else if (result.repeat === 'every-month') {
                        recurringDates = rr.between(result.startDate, result.repeatingEndDate).slice(1);
                    } else if (result.repeat === 'every-year') {
                        recurringDates = rr.between(result.startDate, result.repeatingEndDate).slice(1);
                    } else if (result.repeat === 'custom') {
                        recurringDates = rr.between(result.startDate, result.repeatingEndDate).slice(1);
                    }

                    if (recurringDates.length > 5000) {
                        throw new app.errors.Unprocessable(
                            this.translate('More than 5000 repetitions are not allowed!')
                        );
                    }

                    let i = 0;
                    for (const recurringDate of recurringDates) {
                        const activity = _.omit(result, [
                            '_id',
                            'code',
                            'startDate',
                            'endDate',
                            'repeatStr',
                            'repeat',
                            'repeatingEndDate',
                            'repeatData'
                        ]);
                        activity.code = await app.rpc('kernel.common.request-number', {
                            numberingId: numbering._id,
                            save: true
                        });
                        activity.isRecurrence = true;
                        activity.originalActivityId = result._id;

                        // Start and end dates.
                        const duration = result.endDate.getTime() - result.startDate.getTime();
                        activity.startDate = recurringDate;
                        activity.endDate = app.datetime
                            .fromJSDate(recurringDate)
                            .plus({milliseconds: duration})
                            .toJSDate();

                        // Create activity.
                        const r = await activitiesCollection.create(activity, {
                            user: params.user,
                            userLocation: params.userLocation,
                            setLocation: true,
                            skipEvents: true
                        });

                        // Create notification job.
                        const notificationJobId = await scheduleActivityNotificationJob(app, r);

                        // Update notification.
                        await activitiesCollection.patch(
                            {_id: r._id},
                            {notificationJobId},
                            {
                                user: params.user,
                                skipEvents: !(i === recurringDates.length - 1)
                            }
                        );

                        i++;
                    }
                }

                return result;
            }
        }
    }
];

async function scheduleActivityNotificationJob(app, activity) {
    const now = app.datetime.local().toJSDate();

    // Check and remove old job.
    if (!!activity.notificationJobId) {
        const existingJob = await app.collection('kernel.jobs').findOne({
            _id: activity.notificationJobId,
            $select: ['_id']
        });

        if (!!existingJob) {
            await app.collection('kernel.jobs').remove({_id: activity.notificationJobId});
        }
    }

    // Check if the activity has reminder.
    if (activity.remind === 'never' || activity.status === 'closed' || activity.status === 'canceled') return null;

    // Get schedule time.
    let date = app.datetime.fromJSDate(activity.startDate);
    if (activity.remind === '5-minutes-before') date = date.minus({minutes: 5});
    else if (activity.remind === '10-minutes-before') date = date.minus({minutes: 10});
    else if (activity.remind === '15-minutes-before') date = date.minus({minutes: 15});
    else if (activity.remind === '30-minutes-before') date = date.minus({minutes: 30});
    else if (activity.remind === '1-hour-before') date = date.minus({hours: 1});
    else if (activity.remind === '2-hours-before') date = date.minus({hours: 2});
    else if (activity.remind === '6-hours-before') date = date.minus({hours: 6});
    else if (activity.remind === '12-hours-before') date = date.minus({hours: 12});
    else if (activity.remind === '1-day-before') date = date.minus({days: 1});
    else if (activity.remind === '1-week-before') date = date.minus({weeks: 1});
    else if (activity.remind === '1-month-before') date = date.minus({months: 1});
    date = date.toJSDate();

    if (date.getTime() < now.getTime()) return null;

    // Create job.
    const job = await app.scheduleJob(date, 'activities.notify-activity', {
        _id: activity._id
    });

    // Return job _id.
    return job.attrs._id.toString();
}

function getTypeColor(type) {
    if (type === 'task') return 'blue';
    else if (type === 'meeting') return 'green';
    else if (type === 'appointment') return 'orange';
    else if (type === 'phone-call') return 'purple';
    else if (type === 'event') return 'pink';
    else if (type === 'note') return 'yellow';
}
